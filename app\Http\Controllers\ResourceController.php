<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Resource;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ResourceController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display a listing of the user's resources.
     */
    public function index(Request $request)
    {
        $query = Auth::user()->resources()->with(['tags', 'fileAttachments']);

        // Filter by type if specified
        if ($request->has('type') && $request->type) {
            $query->byType($request->type);
        }

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->where('title', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
        }

        // Filter by visibility
        if ($request->has('visibility') && $request->visibility) {
            $query->where('visibility', $request->visibility);
        }

        $resources = $query->latest()->paginate(12);

        return view('resources.index', compact('resources'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create(Request $request)
    {
        $type = $request->get('type', 'syllabus');
        $tags = Tag::orderBy('name')->get();

        return view('resources.create', compact('type', 'tags'));
    }

    /**
     * Display the specified resource.
     */
    public function show(Resource $resource)
    {
        $this->authorize('view', $resource);

        $resource->load(['user', 'tags', 'fileAttachments', 'comments.user', 'ratings.user']);
        $specificResource = $resource->getSpecificResource();

        return view('resources.show', compact('resource', 'specificResource'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Resource $resource)
    {
        $this->authorize('update', $resource);

        $resource->load(['tags', 'fileAttachments']);
        $specificResource = $resource->getSpecificResource();
        $tags = Tag::orderBy('name')->get();

        return view('resources.edit', compact('resource', 'specificResource', 'tags'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Resource $resource)
    {
        $this->authorize('delete', $resource);

        // Delete associated files
        foreach ($resource->fileAttachments as $file) {
            $file->delete();
        }

        $resource->delete();

        return redirect()->route('resources.index')
            ->with('success', 'Resource deleted successfully.');
    }

    /**
     * Toggle resource visibility
     */
    public function toggleVisibility(Resource $resource)
    {
        $this->authorize('update', $resource);

        $resource->update([
            'visibility' => $resource->visibility === 'public' ? 'private' : 'public'
        ]);

        return back()->with('success', 'Resource visibility updated.');
    }

    /**
     * Share resource via public URL
     */
    public function share(Resource $resource)
    {
        if ($resource->visibility !== 'public') {
            abort(404);
        }

        $resource->load(['user', 'tags', 'fileAttachments']);
        $specificResource = $resource->getSpecificResource();

        return view('resources.public', compact('resource', 'specificResource'));
    }
}
