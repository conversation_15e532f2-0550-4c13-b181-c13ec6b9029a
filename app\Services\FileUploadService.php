<?php

namespace App\Services;

use App\Models\FileAttachment;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class FileUploadService
{
    protected array $allowedMimeTypes = [
        // Documents
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'text/plain',
        
        // Images
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        
        // Archives
        'application/zip',
        'application/x-rar-compressed',
    ];

    protected int $maxFileSize = 50 * 1024 * 1024; // 50MB

    public function uploadFile(UploadedFile $file, $attachable, string $directory = 'uploads'): FileAttachment
    {
        $this->validateFile($file);
        
        $fileName = $this->generateFileName($file);
        $filePath = $file->storeAs($directory, $fileName, 'public');
        $fileHash = hash_file('md5', $file->getRealPath());
        
        return FileAttachment::create([
            'attachable_type' => get_class($attachable),
            'attachable_id' => $attachable->id,
            'original_name' => $file->getClientOriginalName(),
            'file_name' => $fileName,
            'file_path' => $filePath,
            'mime_type' => $file->getMimeType(),
            'file_size' => $file->getSize(),
            'file_hash' => $fileHash,
        ]);
    }

    public function uploadMultipleFiles(array $files, $attachable, string $directory = 'uploads'): array
    {
        $uploadedFiles = [];
        
        foreach ($files as $file) {
            if ($file instanceof UploadedFile) {
                $uploadedFiles[] = $this->uploadFile($file, $attachable, $directory);
            }
        }
        
        return $uploadedFiles;
    }

    public function deleteFile(FileAttachment $fileAttachment): bool
    {
        if (Storage::disk('public')->exists($fileAttachment->file_path)) {
            Storage::disk('public')->delete($fileAttachment->file_path);
        }
        
        return $fileAttachment->delete();
    }

    protected function validateFile(UploadedFile $file): void
    {
        if (!in_array($file->getMimeType(), $this->allowedMimeTypes)) {
            throw new \InvalidArgumentException('File type not allowed: ' . $file->getMimeType());
        }
        
        if ($file->getSize() > $this->maxFileSize) {
            throw new \InvalidArgumentException('File size exceeds maximum allowed size of ' . ($this->maxFileSize / 1024 / 1024) . 'MB');
        }
    }

    protected function generateFileName(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $baseName = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $safeName = Str::slug($baseName);
        
        return $safeName . '_' . time() . '_' . Str::random(8) . '.' . $extension;
    }

    public function getAllowedMimeTypes(): array
    {
        return $this->allowedMimeTypes;
    }

    public function getMaxFileSize(): int
    {
        return $this->maxFileSize;
    }

    public function getFormattedMaxFileSize(): string
    {
        return round($this->maxFileSize / 1024 / 1024, 2) . 'MB';
    }
}
