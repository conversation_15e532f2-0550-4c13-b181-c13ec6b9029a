<?php

namespace Database\Seeders;

use App\Models\Tag;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class TagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tags = [
            ['name' => 'Computer Science', 'color' => '#3B82F6'],
            ['name' => 'Mathematics', 'color' => '#10B981'],
            ['name' => 'Physics', 'color' => '#8B5CF6'],
            ['name' => 'Chemistry', 'color' => '#F59E0B'],
            ['name' => 'Biology', 'color' => '#EF4444'],
            ['name' => 'Programming', 'color' => '#6366F1'],
            ['name' => 'Web Development', 'color' => '#EC4899'],
            ['name' => 'Data Science', 'color' => '#14B8A6'],
            ['name' => 'Machine Learning', 'color' => '#F97316'],
            ['name' => 'Database', 'color' => '#84CC16'],
            ['name' => 'Algorithms', 'color' => '#06B6D4'],
            ['name' => 'Software Engineering', 'color' => '#A855F7'],
            ['name' => 'Networking', 'color' => '#22C55E'],
            ['name' => 'Security', 'color' => '#DC2626'],
            ['name' => 'Mobile Development', 'color' => '#0EA5E9'],
            ['name' => 'Frontend', 'color' => '#F472B6'],
            ['name' => 'Backend', 'color' => '#64748B'],
            ['name' => 'DevOps', 'color' => '#7C3AED'],
            ['name' => 'Cloud Computing', 'color' => '#059669'],
            ['name' => 'Artificial Intelligence', 'color' => '#DC2626'],
        ];

        foreach ($tags as $tagData) {
            Tag::create($tagData);
        }
    }
}
