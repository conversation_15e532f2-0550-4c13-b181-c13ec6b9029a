<?php $__env->startSection('title', 'Create Resource'); ?>

<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex items-center">
            <a href="<?php echo e(route('resources.index')); ?>" class="mr-4 text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Create New Resource')); ?>

            </h2>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="card">
                <div class="mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Choose Resource Type</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <a href="<?php echo e(route('syllabus.create')); ?>" class="resource-type-card <?php echo e($type === 'syllabus' ? 'selected' : ''); ?>">
                            <div class="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-400 transition-colors duration-200 cursor-pointer">
                                <div class="flex items-center mb-3">
                                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                                        <i class="fas fa-file-alt text-xl"></i>
                                    </div>
                                    <h4 class="ml-3 font-semibold text-gray-900">Syllabus</h4>
                                </div>
                                <p class="text-sm text-gray-600">Create a detailed course syllabus with rich formatting and library references.</p>
                            </div>
                        </a>

                        <a href="<?php echo e(route('books.create')); ?>" class="resource-type-card <?php echo e($type === 'book' ? 'selected' : ''); ?>">
                            <div class="p-4 border-2 border-gray-200 rounded-lg hover:border-green-400 transition-colors duration-200 cursor-pointer">
                                <div class="flex items-center mb-3">
                                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                                        <i class="fas fa-book text-xl"></i>
                                    </div>
                                    <h4 class="ml-3 font-semibold text-gray-900">Book</h4>
                                </div>
                                <p class="text-sm text-gray-600">Add textbooks and reference materials with author details and availability status.</p>
                            </div>
                        </a>

                        <a href="<?php echo e(route('slides.create')); ?>" class="resource-type-card <?php echo e($type === 'slide' ? 'selected' : ''); ?>">
                            <div class="p-4 border-2 border-gray-200 rounded-lg hover:border-purple-400 transition-colors duration-200 cursor-pointer">
                                <div class="flex items-center mb-3">
                                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                                        <i class="fas fa-presentation text-xl"></i>
                                    </div>
                                    <h4 class="ml-3 font-semibold text-gray-900">Slides</h4>
                                </div>
                                <p class="text-sm text-gray-600">Upload presentation slides with notes and additional materials.</p>
                            </div>
                        </a>

                        <a href="<?php echo e(route('notes.create')); ?>" class="resource-type-card <?php echo e($type === 'note' ? 'selected' : ''); ?>">
                            <div class="p-4 border-2 border-gray-200 rounded-lg hover:border-yellow-400 transition-colors duration-200 cursor-pointer">
                                <div class="flex items-center mb-3">
                                    <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                                        <i class="fas fa-sticky-note text-xl"></i>
                                    </div>
                                    <h4 class="ml-3 font-semibold text-gray-900">Notes</h4>
                                </div>
                                <p class="text-sm text-gray-600">Share lecture notes, study guides, and supplementary materials.</p>
                            </div>
                        </a>

                        <a href="<?php echo e(route('lab-works.create')); ?>" class="resource-type-card <?php echo e($type === 'lab_work' ? 'selected' : ''); ?>">
                            <div class="p-4 border-2 border-gray-200 rounded-lg hover:border-red-400 transition-colors duration-200 cursor-pointer">
                                <div class="flex items-center mb-3">
                                    <div class="p-3 rounded-full bg-red-100 text-red-600">
                                        <i class="fas fa-flask text-xl"></i>
                                    </div>
                                    <h4 class="ml-3 font-semibold text-gray-900">Lab Work</h4>
                                </div>
                                <p class="text-sm text-gray-600">Create structured lab exercises with multiple sections and instructions.</p>
                            </div>
                        </a>
                    </div>
                </div>

                <div class="border-t border-gray-200 pt-6">
                    <div class="text-center">
                        <p class="text-gray-600 mb-4">Select a resource type above to get started, or</p>
                        <a href="<?php echo e(route('resources.index')); ?>" class="btn-secondary">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Resources
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .resource-type-card.selected .p-4 {
            border-color: #3B82F6;
            background-color: #EFF6FF;
        }
    </style>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web\SaaS Course\CourseLaravel\resources\views/resources/create.blade.php ENDPATH**/ ?>