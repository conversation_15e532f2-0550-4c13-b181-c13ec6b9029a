<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Resource;
use App\Models\Tag;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;

class ResourceTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_view_dashboard(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Teaching Resources Dashboard');
    }

    public function test_user_can_view_resources_index(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/resources');

        $response->assertStatus(200);
        $response->assertSee('My Resources');
    }

    public function test_user_can_create_resource(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->get('/resources/create');

        $response->assertStatus(200);
        $response->assertSee('Create New Resource');
    }

    public function test_guest_redirected_to_login(): void
    {
        $response = $this->get('/dashboard');

        $response->assertRedirect('/login');
    }

    public function test_public_resource_can_be_viewed_by_guest(): void
    {
        $user = User::factory()->create();
        $resource = Resource::factory()->create([
            'user_id' => $user->id,
            'visibility' => 'public',
        ]);

        $response = $this->get("/share/{$resource->share_token}");

        $response->assertStatus(200);
        $response->assertSee($resource->title);
    }
}
