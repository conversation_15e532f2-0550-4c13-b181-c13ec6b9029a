<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lab_sections', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lab_work_id')->constrained()->onDelete('cascade');
            $table->string('section_name'); // e.g., "Lab 1", "Lab 2"
            $table->integer('order_index')->default(0);
            $table->longText('description'); // Rich text content
            $table->text('instructions')->nullable();
            $table->text('important_tips')->nullable();
            $table->timestamps();

            $table->index(['lab_work_id', 'order_index']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lab_sections');
    }
};
