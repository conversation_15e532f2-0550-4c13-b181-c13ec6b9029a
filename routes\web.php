<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\ResourceController;
use App\Http\Controllers\SyllabusController;
use App\Http\Controllers\BookController;
use App\Http\Controllers\SlideController;
use App\Http\Controllers\NoteController;
use App\Http\Controllers\LabWorkController;
use App\Http\Controllers\CommentController;
use App\Http\Controllers\RatingController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return redirect()->route('dashboard');
});

Route::get('/dashboard', function () {
    return view('dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Public resource sharing
Route::get('/share/{resource:share_token}', [ResourceController::class, 'share'])->name('resources.share');

Route::middleware('auth')->group(function () {
    // Profile routes
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');

    // Resource routes
    Route::resource('resources', ResourceController::class)->except(['store']);
    Route::patch('/resources/{resource}/toggle-visibility', [ResourceController::class, 'toggleVisibility'])->name('resources.toggle-visibility');

    // Specific resource type routes
    Route::resource('syllabus', SyllabusController::class)->except(['index', 'show', 'destroy']);
    Route::resource('books', BookController::class)->except(['index', 'show', 'destroy']);
    Route::resource('slides', SlideController::class)->except(['index', 'show', 'destroy']);
    Route::resource('notes', NoteController::class)->except(['index', 'show', 'destroy']);
    Route::resource('lab-works', LabWorkController::class)->except(['index', 'show', 'destroy']);

    // Comments and ratings
    Route::post('/resources/{resource}/comments', [CommentController::class, 'store'])->name('comments.store');
    Route::patch('/comments/{comment}', [CommentController::class, 'update'])->name('comments.update');
    Route::delete('/comments/{comment}', [CommentController::class, 'destroy'])->name('comments.destroy');

    Route::post('/resources/{resource}/ratings', [RatingController::class, 'store'])->name('ratings.store');
    Route::delete('/resources/{resource}/ratings', [RatingController::class, 'destroy'])->name('ratings.destroy');
});

require __DIR__.'/auth.php';
