<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class LabSection extends Model
{
    protected $fillable = [
        'lab_work_id',
        'section_name',
        'order_index',
        'description',
        'instructions',
        'important_tips',
    ];

    protected $casts = [
        'order_index' => 'integer',
    ];

    public function labWork(): BelongsTo
    {
        return $this->belongsTo(LabWork::class);
    }

    public function fileAttachments(): MorphMany
    {
        return $this->morphMany(FileAttachment::class, 'attachable');
    }
}
