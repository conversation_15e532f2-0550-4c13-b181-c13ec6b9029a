<?php

namespace App\Http\Controllers;

use App\Models\Resource;
use App\Models\Syllabus;
use App\Models\Tag;
use App\Services\FileUploadService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SyllabusController extends Controller
{
    protected FileUploadService $fileUploadService;

    public function __construct(FileUploadService $fileUploadService)
    {
        $this->middleware('auth');
        $this->fileUploadService = $fileUploadService;
    }

    /**
     * Show the form for creating a new syllabus.
     */
    public function create()
    {
        $tags = Tag::orderBy('name')->get();
        return view('syllabus.create', compact('tags'));
    }

    /**
     * Store a newly created syllabus in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'formatted_description' => 'required|string',
            'library_references' => 'nullable|array',
            'library_references.*' => 'string',
            'visibility' => 'required|in:public,private',
            'tags' => 'nullable|array',
            'tags.*' => 'exists:tags,id',
            'files.*' => 'nullable|file|max:51200', // 50MB max
        ]);

        DB::transaction(function () use ($request) {
            // Create the base resource
            $resource = Resource::create([
                'user_id' => Auth::id(),
                'type' => 'syllabus',
                'title' => $request->title,
                'description' => $request->description,
                'visibility' => $request->visibility,
            ]);

            // Create the syllabus
            $syllabus = Syllabus::create([
                'resource_id' => $resource->id,
                'formatted_description' => $request->formatted_description,
                'library_references' => $request->library_references ?? [],
            ]);

            // Attach tags
            if ($request->has('tags')) {
                $resource->tags()->attach($request->tags);
            }

            // Handle file uploads
            if ($request->hasFile('files')) {
                foreach ($request->file('files') as $file) {
                    $this->fileUploadService->uploadFile($file, $syllabus, 'syllabus');
                }
            }
        });

        return redirect()->route('resources.index')
            ->with('success', 'Syllabus created successfully.');
    }

    /**
     * Show the form for editing the specified syllabus.
     */
    public function edit(Resource $resource)
    {
        $this->authorize('update', $resource);

        if ($resource->type !== 'syllabus') {
            abort(404);
        }

        $resource->load(['syllabus', 'tags', 'fileAttachments']);
        $tags = Tag::orderBy('name')->get();

        return view('syllabus.edit', compact('resource', 'tags'));
    }

    /**
     * Update the specified syllabus in storage.
     */
    public function update(Request $request, Resource $resource)
    {
        $this->authorize('update', $resource);

        if ($resource->type !== 'syllabus') {
            abort(404);
        }

        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'formatted_description' => 'required|string',
            'library_references' => 'nullable|array',
            'library_references.*' => 'string',
            'visibility' => 'required|in:public,private',
            'tags' => 'nullable|array',
            'tags.*' => 'exists:tags,id',
            'files.*' => 'nullable|file|max:51200',
        ]);

        DB::transaction(function () use ($request, $resource) {
            // Update the base resource
            $resource->update([
                'title' => $request->title,
                'description' => $request->description,
                'visibility' => $request->visibility,
            ]);

            // Update the syllabus
            $resource->syllabus->update([
                'formatted_description' => $request->formatted_description,
                'library_references' => $request->library_references ?? [],
            ]);

            // Sync tags
            $resource->tags()->sync($request->tags ?? []);

            // Handle new file uploads
            if ($request->hasFile('files')) {
                foreach ($request->file('files') as $file) {
                    $this->fileUploadService->uploadFile($file, $resource->syllabus, 'syllabus');
                }
            }
        });

        return redirect()->route('resources.show', $resource)
            ->with('success', 'Syllabus updated successfully.');
    }
}
