<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class LabWork extends Model
{
    protected $fillable = [
        'resource_id',
        'general_description',
        'general_instructions',
        'estimated_duration_hours',
        'difficulty_level',
    ];

    protected $casts = [
        'estimated_duration_hours' => 'integer',
    ];

    public function resource(): BelongsTo
    {
        return $this->belongsTo(Resource::class);
    }

    public function labSections(): HasMany
    {
        return $this->hasMany(LabSection::class)->orderBy('order_index');
    }

    public function fileAttachments(): MorphMany
    {
        return $this->morphMany(FileAttachment::class, 'attachable');
    }

    public function scopeByDifficulty($query, $level)
    {
        return $query->where('difficulty_level', $level);
    }
}
