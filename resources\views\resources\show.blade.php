@extends('layouts.app')

@section('title', $resource->title)

<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <a href="{{ route('resources.index') }}" class="mr-4 text-gray-600 hover:text-gray-800">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div class="flex items-center">
                    <div class="p-2 rounded-full bg-blue-100 text-blue-600 mr-3">
                        @switch($resource->type)
                            @case('syllabus')
                                <i class="fas fa-file-alt"></i>
                                @break
                            @case('book')
                                <i class="fas fa-book"></i>
                                @break
                            @case('slide')
                                <i class="fas fa-presentation"></i>
                                @break
                            @case('note')
                                <i class="fas fa-sticky-note"></i>
                                @break
                            @case('lab_work')
                                <i class="fas fa-flask"></i>
                                @break
                        @endswitch
                    </div>
                    <div>
                        <h2 class="font-semibold text-xl text-gray-800 leading-tight">{{ $resource->title }}</h2>
                        <p class="text-sm text-gray-600">{{ ucfirst(str_replace('_', ' ', $resource->type)) }}</p>
                    </div>
                </div>
            </div>

            <div class="flex items-center space-x-3">
                @if($resource->visibility === 'public')
                    <a href="{{ route('resources.share', $resource->share_token) }}" target="_blank"
                       class="btn-secondary" title="View Public Link">
                        <i class="fas fa-external-link-alt mr-2"></i>Public Link
                    </a>
                @endif

                @can('update', $resource)
                    <a href="{{ route('resources.edit', $resource) }}" class="btn-secondary">
                        <i class="fas fa-edit mr-2"></i>Edit
                    </a>
                @endcan

                <form method="POST" action="{{ route('resources.toggle-visibility', $resource) }}" class="inline">
                    @csrf
                    @method('PATCH')
                    <button type="submit" class="btn-secondary">
                        <i class="fas fa-{{ $resource->visibility === 'public' ? 'eye-slash' : 'eye' }} mr-2"></i>
                        Make {{ $resource->visibility === 'public' ? 'Private' : 'Public' }}
                    </button>
                </form>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Basic Information -->
                    <div class="card">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900">Resource Details</h3>
                            <span class="px-3 py-1 text-sm font-medium rounded-full {{ $resource->visibility === 'public' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                {{ ucfirst($resource->visibility) }}
                            </span>
                        </div>

                        @if($resource->description)
                            <div class="mb-6">
                                <h4 class="font-medium text-gray-900 mb-2">Description</h4>
                                <p class="text-gray-700">{{ $resource->description }}</p>
                            </div>
                        @endif

                        <!-- Type-specific content -->
                        @if($specificResource)
                            @switch($resource->type)
                                @case('syllabus')
                                    <div class="mb-6">
                                        <h4 class="font-medium text-gray-900 mb-3">Syllabus Content</h4>
                                        <div class="prose max-w-none bg-gray-50 p-4 rounded-lg">
                                            {!! $specificResource->formatted_description !!}
                                        </div>
                                    </div>

                                    @if($specificResource->library_references && count($specificResource->library_references) > 0)
                                        <div class="mb-6">
                                            <h4 class="font-medium text-gray-900 mb-3">Library References</h4>
                                            <ul class="space-y-2">
                                                @foreach($specificResource->library_references as $reference)
                                                    <li class="flex items-start">
                                                        <i class="fas fa-book text-blue-600 mt-1 mr-3"></i>
                                                        <span class="text-gray-700">{{ $reference }}</span>
                                                    </li>
                                                @endforeach
                                            </ul>
                                        </div>
                                    @endif
                                    @break

                                @case('book')
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <h4 class="font-medium text-gray-900 mb-2">Author</h4>
                                            <p class="text-gray-700">{{ $specificResource->author_name }}</p>
                                        </div>
                                        <div>
                                            <h4 class="font-medium text-gray-900 mb-2">Publication Year</h4>
                                            <p class="text-gray-700">{{ $specificResource->publication_year }}</p>
                                        </div>
                                        @if($specificResource->edition)
                                            <div>
                                                <h4 class="font-medium text-gray-900 mb-2">Edition</h4>
                                                <p class="text-gray-700">{{ $specificResource->edition }}</p>
                                            </div>
                                        @endif
                                        <div>
                                            <h4 class="font-medium text-gray-900 mb-2">Status</h4>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                                {{ $specificResource->status === 'available' ? 'bg-green-100 text-green-800' :
                                                   ($specificResource->status === 'downloadable' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800') }}">
                                                {{ ucfirst(str_replace('_', ' ', $specificResource->status)) }}
                                            </span>
                                        </div>
                                    </div>
                                    @break

                                @case('lab_work')
                                    <div class="mb-6">
                                        <h4 class="font-medium text-gray-900 mb-3">Lab Description</h4>
                                        <div class="prose max-w-none bg-gray-50 p-4 rounded-lg">
                                            {!! $specificResource->general_description !!}
                                        </div>
                                    </div>

                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        @if($specificResource->estimated_duration_hours)
                                            <div>
                                                <h4 class="font-medium text-gray-900 mb-2">Estimated Duration</h4>
                                                <p class="text-gray-700">{{ $specificResource->estimated_duration_hours }} hours</p>
                                            </div>
                                        @endif
                                        <div>
                                            <h4 class="font-medium text-gray-900 mb-2">Difficulty Level</h4>
                                            <span class="px-2 py-1 text-xs font-medium rounded-full
                                                {{ $specificResource->difficulty_level === 'beginner' ? 'bg-green-100 text-green-800' :
                                                   ($specificResource->difficulty_level === 'intermediate' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800') }}">
                                                {{ ucfirst($specificResource->difficulty_level) }}
                                            </span>
                                        </div>
                                    </div>
                                    @break
                            @endswitch
                        @endif
                    </div>

                    <!-- File Attachments -->
                    @if($resource->fileAttachments->count() > 0)
                        <div class="card">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">File Attachments</h3>
                            <div class="space-y-3">
                                @foreach($resource->fileAttachments as $file)
                                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                                        <div class="flex items-center">
                                            <div class="p-2 rounded-full bg-gray-100 mr-3">
                                                @if($file->isImage())
                                                    <i class="fas fa-image text-green-600"></i>
                                                @elseif($file->isPdf())
                                                    <i class="fas fa-file-pdf text-red-600"></i>
                                                @else
                                                    <i class="fas fa-file text-gray-600"></i>
                                                @endif
                                            </div>
                                            <div>
                                                <h4 class="font-medium text-gray-900">{{ $file->original_name }}</h4>
                                                <p class="text-sm text-gray-600">{{ $file->formatted_size }} • {{ $file->download_count }} downloads</p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <a href="{{ $file->url }}" target="_blank"
                                               onclick="incrementDownload({{ $file->id }})"
                                               class="btn-primary">
                                                <i class="fas fa-download mr-2"></i>Download
                                            </a>
                                            @can('update', $resource)
                                                <form method="POST" action="{{ route('files.destroy', $file) }}" class="inline" onsubmit="return confirm('Are you sure?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="p-2 text-red-600 hover:text-red-800">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            @endif
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="lg:col-span-1 space-y-6">
                    <!-- Statistics -->
                    <div class="card">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Statistics</h3>
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Downloads</span>
                                <span class="font-medium">{{ $resource->download_count }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Views</span>
                                <span class="font-medium">{{ $resource->view_count ?? 0 }}</span>
                            </div>
                            @if($resource->average_rating)
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Rating</span>
                                    <div class="flex items-center">
                                        <div class="flex text-yellow-400 mr-2">
                                            @for($i = 1; $i <= 5; $i++)
                                                <i class="fas fa-star text-xs {{ $i <= $resource->average_rating ? '' : 'text-gray-300' }}"></i>
                                            @endfor
                                        </div>
                                        <span class="text-sm">{{ number_format($resource->average_rating, 1) }}</span>
                                    </div>
                                </div>
                            @endif
                            <div class="flex justify-between">
                                <span class="text-gray-600">Created</span>
                                <span class="font-medium">{{ $resource->created_at->diffForHumans() }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Tags -->
                    @if($resource->tags->count() > 0)
                        <div class="card">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Tags</h3>
                            <div class="flex flex-wrap gap-2">
                                @foreach($resource->tags as $tag)
                                    <span class="px-3 py-1 text-sm font-medium rounded-full bg-blue-100 text-blue-800">
                                        {{ $tag->name }}
                                    </span>
                                @endforeach
                            </div>
                        </div>
                    @endif

                    <!-- Share Options -->
                    @if($resource->visibility === 'public')
                        <div class="card">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Share This Resource</h3>
                            <div class="space-y-3">
                                <div>
                                    <label class="form-label">Public URL</label>
                                    <div class="flex">
                                        <input type="text"
                                               value="{{ route('resources.share', $resource->share_token) }}"
                                               class="form-input rounded-r-none"
                                               readonly
                                               id="share-url">
                                        <button onclick="copyToClipboard()"
                                                class="px-3 py-2 bg-gray-200 border border-l-0 border-gray-300 rounded-r-md hover:bg-gray-300">
                                            <i class="fas fa-copy"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <!-- Comments and Ratings -->
                    <x-comments-section :resource="$resource" />
                </div>
            </div>
        </div>
    </div>

    <script>
        function incrementDownload(fileId) {
            fetch(`/api/files/${fileId}/download`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            });
        }

        function copyToClipboard() {
            const shareUrl = document.getElementById('share-url');
            shareUrl.select();
            shareUrl.setSelectionRange(0, 99999);
            navigator.clipboard.writeText(shareUrl.value);

            // Show feedback
            const button = event.target.closest('button');
            const originalContent = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check text-green-600"></i>';
            setTimeout(() => {
                button.innerHTML = originalContent;
            }, 2000);
        }
    </script>
</x-app-layout>
