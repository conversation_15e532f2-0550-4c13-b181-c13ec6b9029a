<?php $__env->startSection('title', 'My Resources'); ?>

<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('My Resources')); ?>

            </h2>
            <a href="<?php echo e(route('resources.create')); ?>" class="btn-primary">
                <i class="fas fa-plus mr-2"></i>Create Resource
            </a>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Filters -->
            <div class="card mb-6">
                <form method="GET" action="<?php echo e(route('resources.index')); ?>" class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-64">
                        <input type="text" name="search" value="<?php echo e(request('search')); ?>" 
                               placeholder="Search resources..." 
                               class="form-input">
                    </div>
                    
                    <select name="type" class="form-input w-auto">
                        <option value="">All Types</option>
                        <option value="syllabus" <?php echo e(request('type') === 'syllabus' ? 'selected' : ''); ?>>Syllabus</option>
                        <option value="book" <?php echo e(request('type') === 'book' ? 'selected' : ''); ?>>Books</option>
                        <option value="slide" <?php echo e(request('type') === 'slide' ? 'selected' : ''); ?>>Slides</option>
                        <option value="note" <?php echo e(request('type') === 'note' ? 'selected' : ''); ?>>Notes</option>
                        <option value="lab_work" <?php echo e(request('type') === 'lab_work' ? 'selected' : ''); ?>>Lab Works</option>
                    </select>
                    
                    <select name="visibility" class="form-input w-auto">
                        <option value="">All Visibility</option>
                        <option value="public" <?php echo e(request('visibility') === 'public' ? 'selected' : ''); ?>>Public</option>
                        <option value="private" <?php echo e(request('visibility') === 'private' ? 'selected' : ''); ?>>Private</option>
                    </select>
                    
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-search mr-2"></i>Filter
                    </button>
                    
                    <?php if(request()->hasAny(['search', 'type', 'visibility'])): ?>
                        <a href="<?php echo e(route('resources.index')); ?>" class="btn-secondary">
                            <i class="fas fa-times mr-2"></i>Clear
                        </a>
                    <?php endif; ?>
                </form>
            </div>

            <!-- Resources Grid -->
            <?php if($resources->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $resources; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $resource): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="card hover:shadow-lg transition-shadow duration-200">
                            <!-- Resource Header -->
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="p-2 rounded-full bg-gray-100">
                                        <?php switch($resource->type):
                                            case ('syllabus'): ?>
                                                <i class="fas fa-file-alt text-blue-600"></i>
                                                <?php break; ?>
                                            <?php case ('book'): ?>
                                                <i class="fas fa-book text-green-600"></i>
                                                <?php break; ?>
                                            <?php case ('slide'): ?>
                                                <i class="fas fa-presentation text-purple-600"></i>
                                                <?php break; ?>
                                            <?php case ('note'): ?>
                                                <i class="fas fa-sticky-note text-yellow-600"></i>
                                                <?php break; ?>
                                            <?php case ('lab_work'): ?>
                                                <i class="fas fa-flask text-red-600"></i>
                                                <?php break; ?>
                                        <?php endswitch; ?>
                                    </div>
                                    <span class="ml-2 text-sm font-medium text-gray-600"><?php echo e(ucfirst(str_replace('_', ' ', $resource->type))); ?></span>
                                </div>
                                
                                <div class="flex items-center space-x-2">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo e($resource->visibility === 'public' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'); ?>">
                                        <?php echo e(ucfirst($resource->visibility)); ?>

                                    </span>
                                    
                                    <!-- Actions Dropdown -->
                                    <div class="relative" x-data="{ open: false }">
                                        <button @click="open = !open" class="p-1 rounded-full hover:bg-gray-100">
                                            <i class="fas fa-ellipsis-v text-gray-400"></i>
                                        </button>
                                        <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 z-10 mt-2 w-48 bg-white rounded-md shadow-lg py-1">
                                            <a href="<?php echo e(route('resources.show', $resource)); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <i class="fas fa-eye mr-2"></i>View
                                            </a>
                                            <a href="<?php echo e(route('resources.edit', $resource)); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                <i class="fas fa-edit mr-2"></i>Edit
                                            </a>
                                            <form method="POST" action="<?php echo e(route('resources.toggle-visibility', $resource)); ?>" class="inline">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('PATCH'); ?>
                                                <button type="submit" class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-<?php echo e($resource->visibility === 'public' ? 'eye-slash' : 'eye'); ?> mr-2"></i>
                                                    Make <?php echo e($resource->visibility === 'public' ? 'Private' : 'Public'); ?>

                                                </button>
                                            </form>
                                            <?php if($resource->visibility === 'public'): ?>
                                                <a href="<?php echo e(route('resources.share', $resource->share_token)); ?>" target="_blank" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                                    <i class="fas fa-share mr-2"></i>Share Link
                                                </a>
                                            <?php endif; ?>
                                            <div class="border-t border-gray-100"></div>
                                            <form method="POST" action="<?php echo e(route('resources.destroy', $resource)); ?>" class="inline" onsubmit="return confirm('Are you sure you want to delete this resource?')">
                                                <?php echo csrf_field(); ?>
                                                <?php echo method_field('DELETE'); ?>
                                                <button type="submit" class="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                                    <i class="fas fa-trash mr-2"></i>Delete
                                                </button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Resource Content -->
                            <div class="mb-4">
                                <h3 class="font-semibold text-lg text-gray-900 mb-2"><?php echo e($resource->title); ?></h3>
                                <?php if($resource->description): ?>
                                    <p class="text-gray-600 text-sm line-clamp-3"><?php echo e($resource->description); ?></p>
                                <?php endif; ?>
                            </div>

                            <!-- Tags -->
                            <?php if($resource->tags->count() > 0): ?>
                                <div class="flex flex-wrap gap-1 mb-4">
                                    <?php $__currentLoopData = $resource->tags->take(3); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                                            <?php echo e($tag->name); ?>

                                        </span>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php if($resource->tags->count() > 3): ?>
                                        <span class="px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-600">
                                            +<?php echo e($resource->tags->count() - 3); ?> more
                                        </span>
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>

                            <!-- Stats -->
                            <div class="flex items-center justify-between text-sm text-gray-500 border-t border-gray-200 pt-4">
                                <div class="flex items-center space-x-4">
                                    <span><i class="fas fa-download mr-1"></i><?php echo e($resource->download_count); ?></span>
                                    <?php if($resource->average_rating): ?>
                                        <span><i class="fas fa-star mr-1 text-yellow-400"></i><?php echo e(number_format($resource->average_rating, 1)); ?></span>
                                    <?php endif; ?>
                                    <span><i class="fas fa-paperclip mr-1"></i><?php echo e($resource->fileAttachments->count()); ?></span>
                                </div>
                                <span><?php echo e($resource->created_at->diffForHumans()); ?></span>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="mt-8">
                    <?php echo e($resources->withQueryString()->links()); ?>

                </div>
            <?php else: ?>
                <div class="card text-center py-12">
                    <i class="fas fa-folder-open text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No resources found</h3>
                    <p class="text-gray-600 mb-4">
                        <?php if(request()->hasAny(['search', 'type', 'visibility'])): ?>
                            Try adjusting your filters or create a new resource.
                        <?php else: ?>
                            Start by creating your first teaching resource.
                        <?php endif; ?>
                    </p>
                    <a href="<?php echo e(route('resources.create')); ?>" class="btn-primary">
                        <i class="fas fa-plus mr-2"></i>Create Resource
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web\SaaS Course\CourseLaravel\resources\views/resources/index.blade.php ENDPATH**/ ?>