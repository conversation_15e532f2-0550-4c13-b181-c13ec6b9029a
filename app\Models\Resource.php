<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Str;

class Resource extends Model
{
    protected $fillable = [
        'user_id',
        'type',
        'title',
        'description',
        'visibility',
        'share_token',
        'is_active',
        'download_count',
        'average_rating',
        'rating_count',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'download_count' => 'integer',
        'average_rating' => 'decimal:2',
        'rating_count' => 'integer',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($resource) {
            if (empty($resource->share_token)) {
                $resource->share_token = Str::random(32);
            }
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function syllabus(): HasOne
    {
        return $this->hasOne(Syllabus::class);
    }

    public function book(): HasOne
    {
        return $this->hasOne(Book::class);
    }

    public function slide(): HasOne
    {
        return $this->hasOne(Slide::class);
    }

    public function note(): HasOne
    {
        return $this->hasOne(Note::class);
    }

    public function labWork(): HasOne
    {
        return $this->hasOne(LabWork::class);
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class)->withTimestamps();
    }

    public function fileAttachments(): MorphMany
    {
        return $this->morphMany(FileAttachment::class, 'attachable');
    }

    public function comments(): HasMany
    {
        return $this->hasMany(Comment::class);
    }

    public function ratings(): HasMany
    {
        return $this->hasMany(Rating::class);
    }

    // Scopes
    public function scopePublic($query)
    {
        return $query->where('visibility', 'public');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    // Helper methods
    public function getSpecificResource()
    {
        return match($this->type) {
            'syllabus' => $this->syllabus,
            'book' => $this->book,
            'slide' => $this->slide,
            'note' => $this->note,
            'lab_work' => $this->labWork,
            default => null,
        };
    }

    public function incrementDownloadCount()
    {
        $this->increment('download_count');
    }

    public function updateRating()
    {
        $ratings = $this->ratings();
        $this->update([
            'average_rating' => $ratings->avg('rating'),
            'rating_count' => $ratings->count(),
        ]);
    }
}
