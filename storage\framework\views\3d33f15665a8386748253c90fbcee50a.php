<?php $__env->startSection('title', 'Create Syllabus'); ?>

<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex items-center">
            <a href="<?php echo e(route('resources.create')); ?>" class="mr-4 text-gray-600 hover:text-gray-800">
                <i class="fas fa-arrow-left"></i>
            </a>
            <div class="flex items-center">
                <div class="p-2 rounded-full bg-blue-100 text-blue-600 mr-3">
                    <i class="fas fa-file-alt"></i>
                </div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    <?php echo e(__('Create Syllabus')); ?>

                </h2>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <form method="POST" action="<?php echo e(route('syllabus.store')); ?>" enctype="multipart/form-data" x-data="syllabusForm()">
                <?php echo csrf_field(); ?>

                <div class="space-y-6">
                    <!-- Basic Information -->
                    <div class="card">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="md:col-span-2">
                                <label for="title" class="form-label">Title *</label>
                                <input type="text" id="title" name="title" value="<?php echo e(old('title')); ?>"
                                       class="form-input" required>
                                <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div class="md:col-span-2">
                                <label for="description" class="form-label">Short Description</label>
                                <textarea id="description" name="description" rows="3"
                                          class="form-input"><?php echo e(old('description')); ?></textarea>
                                <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>

                            <div>
                                <label for="visibility" class="form-label">Visibility *</label>
                                <select id="visibility" name="visibility" class="form-input" required>
                                    <option value="private" <?php echo e(old('visibility') === 'private' ? 'selected' : ''); ?>>Private</option>
                                    <option value="public" <?php echo e(old('visibility') === 'public' ? 'selected' : ''); ?>>Public</option>
                                </select>
                                <?php $__errorArgs = ['visibility'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Content -->
                    <div class="card">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Detailed Content</h3>

                        <div class="mb-6">
                            <label for="formatted_description" class="form-label">Formatted Description *</label>
                            <?php if (isset($component)) { $__componentOriginald7ed76000a5d14eac13d9a7b06fc15fe = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginald7ed76000a5d14eac13d9a7b06fc15fe = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.wysiwyg-editor','data' => ['name' => 'formatted_description','value' => old('formatted_description'),'placeholder' => 'Enter your detailed syllabus content here. You can use headings, lists, links, and formatting to structure your content.','required' => 'true','height' => '400']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('wysiwyg-editor'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'formatted_description','value' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(old('formatted_description')),'placeholder' => 'Enter your detailed syllabus content here. You can use headings, lists, links, and formatting to structure your content.','required' => 'true','height' => '400']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginald7ed76000a5d14eac13d9a7b06fc15fe)): ?>
<?php $attributes = $__attributesOriginald7ed76000a5d14eac13d9a7b06fc15fe; ?>
<?php unset($__attributesOriginald7ed76000a5d14eac13d9a7b06fc15fe); ?>
<?php endif; ?>
<?php if (isset($__componentOriginald7ed76000a5d14eac13d9a7b06fc15fe)): ?>
<?php $component = $__componentOriginald7ed76000a5d14eac13d9a7b06fc15fe; ?>
<?php unset($__componentOriginald7ed76000a5d14eac13d9a7b06fc15fe); ?>
<?php endif; ?>
                            <?php $__errorArgs = ['formatted_description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <p class="mt-1 text-sm text-red-600"><?php echo e($message); ?></p>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Library References -->
                    <div class="card">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Library References</h3>

                        <div x-data="{ references: <?php echo e(json_encode(old('library_references', []))); ?> }">
                            <div class="space-y-3">
                                <template x-for="(reference, index) in references" :key="index">
                                    <div class="flex items-center space-x-3">
                                        <input type="text" :name="'library_references[' + index + ']'"
                                               x-model="references[index]"
                                               placeholder="Enter library reference or resource link"
                                               class="form-input flex-1">
                                        <button type="button" @click="references.splice(index, 1)"
                                                class="p-2 text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </template>
                            </div>

                            <button type="button" @click="references.push('')"
                                    class="mt-3 btn-secondary">
                                <i class="fas fa-plus mr-2"></i>Add Reference
                            </button>
                        </div>
                    </div>

                    <!-- Tags -->
                    <div class="card">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Tags</h3>

                        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                            <?php $__currentLoopData = $tags; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tag): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <label class="flex items-center">
                                    <input type="checkbox" name="tags[]" value="<?php echo e($tag->id); ?>"
                                           class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                           <?php echo e(in_array($tag->id, old('tags', [])) ? 'checked' : ''); ?>>
                                    <span class="ml-2 text-sm text-gray-700"><?php echo e($tag->name); ?></span>
                                </label>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>

                    <!-- File Uploads -->
                    <div class="card">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">File Attachments</h3>
                        <p class="text-sm text-gray-600 mb-4">Upload supporting documents, PDFs, or other materials related to your syllabus.</p>

                        <?php if (isset($component)) { $__componentOriginal22dd814e34b3292120e6fee9433ec671 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal22dd814e34b3292120e6fee9433ec671 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.file-upload','data' => ['name' => 'files[]','accept' => '.pdf,.doc,.docx,.ppt,.pptx,.txt,.rtf','multiple' => true,'maxSize' => '50MB']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('file-upload'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['name' => 'files[]','accept' => '.pdf,.doc,.docx,.ppt,.pptx,.txt,.rtf','multiple' => true,'maxSize' => '50MB']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal22dd814e34b3292120e6fee9433ec671)): ?>
<?php $attributes = $__attributesOriginal22dd814e34b3292120e6fee9433ec671; ?>
<?php unset($__attributesOriginal22dd814e34b3292120e6fee9433ec671); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal22dd814e34b3292120e6fee9433ec671)): ?>
<?php $component = $__componentOriginal22dd814e34b3292120e6fee9433ec671; ?>
<?php unset($__componentOriginal22dd814e34b3292120e6fee9433ec671); ?>
<?php endif; ?>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="flex justify-end space-x-4">
                        <a href="<?php echo e(route('resources.create')); ?>" class="btn-secondary">
                            Cancel
                        </a>
                        <button type="submit" class="btn-primary">
                            <i class="fas fa-save mr-2"></i>Create Syllabus
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <?php echo $__env->yieldPushContent('scripts'); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\web\SaaS Course\CourseLaravel\resources\views/syllabus/create.blade.php ENDPATH**/ ?>