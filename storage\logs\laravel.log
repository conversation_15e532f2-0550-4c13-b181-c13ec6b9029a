[2025-06-24 13:48:17] local.ERROR: SQLSTATE[HY000]: General error: 1 index file_attachments_attachable_type_attachable_id_index already exists (Connection: sqlite, SQL: create index "file_attachments_attachable_type_attachable_id_index" on "file_attachments" ("attachable_type", "attachable_id")) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 index file_attachments_attachable_type_attachable_id_index already exists (Connection: sqlite, SQL: create index \"file_attachments_attachable_type_attachable_id_index\" on \"file_attachments\" (\"attachable_type\", \"attachable_id\")) at D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create index \"f...', Array, Object(Closure))
#1 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create index \"f...', Array, Object(Closure))
#2 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create index \"f...')
#3 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('file_attachment...', Object(Closure))
#6 D:\\web\\SaaS Course\\CourseLaravel\\database\\migrations\\2025_06_24_132230_create_file_attachments_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_24_1322...', Object(Closure))
#13 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_24_1322...', Object(Closure))
#14 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\web\\\\SaaS Cou...', 1, false)
#15 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#26 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\web\\SaaS Course\\CourseLaravel\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 index file_attachments_attachable_type_attachable_id_index already exists at D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create index \"f...')
#1 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create index \"f...', Array)
#2 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create index \"f...', Array, Object(Closure))
#3 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create index \"f...', Array, Object(Closure))
#4 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create index \"f...')
#5 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('file_attachment...', Object(Closure))
#8 D:\\web\\SaaS Course\\CourseLaravel\\database\\migrations\\2025_06_24_132230_create_file_attachments_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_24_1322...', Object(Closure))
#15 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_24_1322...', Object(Closure))
#16 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\web\\\\SaaS Cou...', 1, false)
#17 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(209): Illuminate\\Container\\Container->call(Array)
#28 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(178): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 D:\\web\\SaaS Course\\CourseLaravel\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-24 13:49:08] local.ERROR: Command "make:service" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"make:service\" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view at D:\\web\\SaaS Course\\CourseLaravel\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find('make:service')
#1 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\web\\SaaS Course\\CourseLaravel\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 D:\\web\\SaaS Course\\CourseLaravel\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#5 {main}
"} 
