<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                <?php echo e(__('Teaching Resources Dashboard')); ?>

            </h2>
            <a href="<?php echo e(route('resources.create')); ?>" class="btn-primary">
                <i class="fas fa-plus mr-2"></i>Create Resource
            </a>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="card">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-file-alt text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Resources</p>
                            <p class="text-2xl font-semibold text-gray-900"><?php echo e(auth()->user()->resources()->count()); ?></p>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-eye text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Public Resources</p>
                            <p class="text-2xl font-semibold text-gray-900"><?php echo e(auth()->user()->resources()->public()->count()); ?></p>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                            <i class="fas fa-download text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Downloads</p>
                            <p class="text-2xl font-semibold text-gray-900"><?php echo e(auth()->user()->resources()->sum('download_count')); ?></p>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-star text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Avg Rating</p>
                            <p class="text-2xl font-semibold text-gray-900">
                                <?php echo e(number_format(auth()->user()->resources()->whereNotNull('average_rating')->avg('average_rating'), 1) ?: 'N/A'); ?>

                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Resources -->
            <div class="card">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Resources</h3>
                    <a href="<?php echo e(route('resources.index')); ?>" class="text-blue-600 hover:text-blue-800">View All</a>
                </div>

                <?php
                    $recentResources = auth()->user()->resources()->with(['tags', 'fileAttachments'])->latest()->take(5)->get();
                ?>

                <?php if($recentResources->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $recentResources; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $resource): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                                <div class="flex items-center">
                                    <div class="p-2 rounded-full bg-gray-100">
                                        <?php switch($resource->type):
                                            case ('syllabus'): ?>
                                                <i class="fas fa-file-alt text-blue-600"></i>
                                                <?php break; ?>
                                            <?php case ('book'): ?>
                                                <i class="fas fa-book text-green-600"></i>
                                                <?php break; ?>
                                            <?php case ('slide'): ?>
                                                <i class="fas fa-presentation text-purple-600"></i>
                                                <?php break; ?>
                                            <?php case ('note'): ?>
                                                <i class="fas fa-sticky-note text-yellow-600"></i>
                                                <?php break; ?>
                                            <?php case ('lab_work'): ?>
                                                <i class="fas fa-flask text-red-600"></i>
                                                <?php break; ?>
                                        <?php endswitch; ?>
                                    </div>
                                    <div class="ml-4">
                                        <h4 class="font-medium text-gray-900"><?php echo e($resource->title); ?></h4>
                                        <p class="text-sm text-gray-600"><?php echo e(ucfirst($resource->type)); ?> • <?php echo e($resource->created_at->diffForHumans()); ?></p>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-4">
                                    <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo e($resource->visibility === 'public' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'); ?>">
                                        <?php echo e(ucfirst($resource->visibility)); ?>

                                    </span>
                                    <a href="<?php echo e(route('resources.show', $resource)); ?>" class="text-blue-600 hover:text-blue-800">
                                        <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <i class="fas fa-folder-open text-4xl text-gray-400 mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No resources yet</h3>
                        <p class="text-gray-600 mb-4">Start by creating your first teaching resource.</p>
                        <a href="<?php echo e(route('resources.create')); ?>" class="btn-primary">
                            <i class="fas fa-plus mr-2"></i>Create Your First Resource
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH D:\web\SaaS Course\CourseLaravel\resources\views/dashboard.blade.php ENDPATH**/ ?>