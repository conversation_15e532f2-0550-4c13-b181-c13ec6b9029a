<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['name' => 'files[]', 'accept' => '.pdf,.doc,.docx,.ppt,.pptx', 'multiple' => true, 'maxSize' => '50MB']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['name' => 'files[]', 'accept' => '.pdf,.doc,.docx,.ppt,.pptx', 'multiple' => true, 'maxSize' => '50MB']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div x-data="fileUpload()" class="space-y-4">
    <!-- Drag and Drop Area -->
    <div class="drag-drop-area" 
         :class="{ 'dragover': dragover }"
         @drop="handleDrop($event)"
         @dragover="handleDragOver($event)"
         @dragleave="handleDragLeave()">
        <div class="text-center">
            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
            <p class="text-lg font-medium text-gray-900 mb-2">Drop files here or click to browse</p>
            <p class="text-sm text-gray-600 mb-4">Supports <?php echo e(str_replace(',', ', ', $accept)); ?> files up to <?php echo e($maxSize); ?></p>
            <input type="file" <?php echo e($multiple ? 'multiple' : ''); ?> name="<?php echo e($name); ?>" @change="addFiles($event)" 
                   accept="<?php echo e($accept); ?>" class="hidden" id="file-input-<?php echo e(Str::random(8)); ?>">
            <label for="file-input-<?php echo e(Str::random(8)); ?>" class="btn-primary cursor-pointer">
                <i class="fas fa-plus mr-2"></i>Choose Files
            </label>
        </div>
    </div>
    
    <!-- Selected Files -->
    <div x-show="files.length > 0" class="space-y-2">
        <h4 class="font-medium text-gray-900">Selected Files:</h4>
        <template x-for="(file, index) in files" :key="index">
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div class="flex items-center">
                    <i class="fas fa-file text-gray-400 mr-3"></i>
                    <span class="text-sm text-gray-900" x-text="file.name"></span>
                    <span class="text-xs text-gray-500 ml-2" x-text="formatFileSize(file.size)"></span>
                </div>
                <button type="button" @click="removeFile(index)" 
                        class="text-red-600 hover:text-red-800">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </template>
    </div>
</div>

<script>
    function fileUpload() {
        return {
            files: [],
            dragover: false,
            
            addFiles(event) {
                const newFiles = Array.from(event.target.files || event.dataTransfer.files);
                this.files = [...this.files, ...newFiles];
            },
            
            removeFile(index) {
                this.files.splice(index, 1);
            },
            
            handleDrop(event) {
                event.preventDefault();
                this.dragover = false;
                this.addFiles(event);
            },
            
            handleDragOver(event) {
                event.preventDefault();
                this.dragover = true;
            },
            
            handleDragLeave() {
                this.dragover = false;
            },
            
            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }
        }
    }
</script>
<?php /**PATH D:\web\SaaS Course\CourseLaravel\resources\views/components/file-upload.blade.php ENDPATH**/ ?>