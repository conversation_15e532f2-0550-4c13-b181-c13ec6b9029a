@props(['resource'])

<div class="space-y-6">
    <!-- Rating Section -->
    <div class="card">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Rate This Resource</h3>
        
        @auth
            @php
                $userRating = $resource->ratings()->where('user_id', auth()->id())->first();
            @endphp
            
            <form method="POST" action="{{ route('ratings.store', $resource) }}" x-data="ratingForm({{ $userRating?->rating ?? 0 }})">
                @csrf
                <div class="mb-4">
                    <div class="flex items-center space-x-1 mb-2">
                        <span class="text-sm font-medium text-gray-700 mr-2">Your Rating:</span>
                        @for($i = 1; $i <= 5; $i++)
                            <button type="button" 
                                    @click="setRating({{ $i }})"
                                    class="text-2xl focus:outline-none"
                                    :class="rating >= {{ $i }} ? 'text-yellow-400' : 'text-gray-300'">
                                <i class="fas fa-star"></i>
                            </button>
                        @endfor
                    </div>
                    <input type="hidden" name="rating" x-model="rating">
                </div>
                
                <div class="mb-4">
                    <label for="review" class="form-label">Review (Optional)</label>
                    <textarea name="review" id="review" rows="3" 
                              class="form-input" 
                              placeholder="Share your thoughts about this resource...">{{ $userRating?->review }}</textarea>
                </div>
                
                <div class="flex space-x-3">
                    <button type="submit" class="btn-primary" x-show="rating > 0">
                        <i class="fas fa-star mr-2"></i>Submit Rating
                    </button>
                    
                    @if($userRating)
                        <form method="POST" action="{{ route('ratings.destroy', $resource) }}" class="inline">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn-secondary">
                                <i class="fas fa-trash mr-2"></i>Remove Rating
                            </button>
                        </form>
                    @endif
                </div>
            </form>
        @else
            <p class="text-gray-600">
                <a href="{{ route('login') }}" class="text-blue-600 hover:text-blue-800">Login</a> to rate this resource.
            </p>
        @endauth
        
        <!-- Average Rating Display -->
        @if($resource->average_rating)
            <div class="mt-6 pt-6 border-t border-gray-200">
                <div class="flex items-center justify-between">
                    <div>
                        <div class="flex items-center">
                            <div class="flex text-yellow-400 mr-2">
                                @for($i = 1; $i <= 5; $i++)
                                    <i class="fas fa-star {{ $i <= $resource->average_rating ? '' : 'text-gray-300' }}"></i>
                                @endfor
                            </div>
                            <span class="text-lg font-medium">{{ number_format($resource->average_rating, 1) }}</span>
                        </div>
                        <p class="text-sm text-gray-600">Based on {{ $resource->rating_count }} {{ Str::plural('review', $resource->rating_count) }}</p>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Comments Section -->
    <div class="card">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Comments</h3>
        
        @auth
            <!-- Add Comment Form -->
            <form method="POST" action="{{ route('comments.store', $resource) }}" class="mb-6">
                @csrf
                <div class="mb-4">
                    <label for="comment" class="form-label">Add a Comment</label>
                    <textarea name="content" id="comment" rows="3" 
                              class="form-input" 
                              placeholder="Share your thoughts or ask questions about this resource..."
                              required></textarea>
                </div>
                <button type="submit" class="btn-primary">
                    <i class="fas fa-comment mr-2"></i>Post Comment
                </button>
            </form>
        @else
            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                <p class="text-gray-600">
                    <a href="{{ route('login') }}" class="text-blue-600 hover:text-blue-800">Login</a> to join the discussion.
                </p>
            </div>
        @endauth
        
        <!-- Comments List -->
        @php
            $comments = $resource->comments()->with(['user', 'replies.user'])->whereNull('parent_id')->latest()->get();
        @endphp
        
        @if($comments->count() > 0)
            <div class="space-y-6">
                @foreach($comments as $comment)
                    <div class="comment" x-data="{ editing: false, replying: false }">
                        <!-- Main Comment -->
                        <div class="flex space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <span class="text-sm font-medium text-blue-600">
                                        {{ substr($comment->user->name, 0, 1) }}
                                    </span>
                                </div>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center space-x-2 mb-1">
                                    <h4 class="font-medium text-gray-900">{{ $comment->user->name }}</h4>
                                    <span class="text-sm text-gray-500">{{ $comment->created_at->diffForHumans() }}</span>
                                    @if($comment->created_at != $comment->updated_at)
                                        <span class="text-xs text-gray-400">(edited)</span>
                                    @endif
                                </div>
                                
                                <!-- Comment Content -->
                                <div x-show="!editing">
                                    <p class="text-gray-700">{{ $comment->content }}</p>
                                </div>
                                
                                <!-- Edit Form -->
                                <div x-show="editing" style="display: none;">
                                    <form method="POST" action="{{ route('comments.update', $comment) }}">
                                        @csrf
                                        @method('PATCH')
                                        <textarea name="content" rows="3" class="form-input mb-2" required>{{ $comment->content }}</textarea>
                                        <div class="flex space-x-2">
                                            <button type="submit" class="btn-primary text-sm">Save</button>
                                            <button type="button" @click="editing = false" class="btn-secondary text-sm">Cancel</button>
                                        </div>
                                    </form>
                                </div>
                                
                                <!-- Comment Actions -->
                                <div class="flex items-center space-x-4 mt-2">
                                    @auth
                                        <button @click="replying = !replying" class="text-sm text-gray-500 hover:text-gray-700">
                                            <i class="fas fa-reply mr-1"></i>Reply
                                        </button>
                                        
                                        @can('update', $comment)
                                            <button @click="editing = !editing" class="text-sm text-gray-500 hover:text-gray-700">
                                                <i class="fas fa-edit mr-1"></i>Edit
                                            </button>
                                        @endcan
                                        
                                        @can('delete', $comment)
                                            <form method="POST" action="{{ route('comments.destroy', $comment) }}" class="inline" onsubmit="return confirm('Are you sure?')">
                                                @csrf
                                                @method('DELETE')
                                                <button type="submit" class="text-sm text-red-600 hover:text-red-800">
                                                    <i class="fas fa-trash mr-1"></i>Delete
                                                </button>
                                            </form>
                                        @endcan
                                    @endauth
                                </div>
                                
                                <!-- Reply Form -->
                                @auth
                                    <div x-show="replying" style="display: none;" class="mt-3">
                                        <form method="POST" action="{{ route('comments.store', $resource) }}">
                                            @csrf
                                            <input type="hidden" name="parent_id" value="{{ $comment->id }}">
                                            <textarea name="content" rows="2" class="form-input mb-2" placeholder="Write a reply..." required></textarea>
                                            <div class="flex space-x-2">
                                                <button type="submit" class="btn-primary text-sm">Reply</button>
                                                <button type="button" @click="replying = false" class="btn-secondary text-sm">Cancel</button>
                                            </div>
                                        </form>
                                    </div>
                                @endauth
                                
                                <!-- Replies -->
                                @if($comment->replies->count() > 0)
                                    <div class="mt-4 space-y-4">
                                        @foreach($comment->replies as $reply)
                                            <div class="flex space-x-3 pl-4 border-l-2 border-gray-200">
                                                <div class="flex-shrink-0">
                                                    <div class="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center">
                                                        <span class="text-xs font-medium text-gray-600">
                                                            {{ substr($reply->user->name, 0, 1) }}
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="flex-1">
                                                    <div class="flex items-center space-x-2 mb-1">
                                                        <h5 class="text-sm font-medium text-gray-900">{{ $reply->user->name }}</h5>
                                                        <span class="text-xs text-gray-500">{{ $reply->created_at->diffForHumans() }}</span>
                                                    </div>
                                                    <p class="text-sm text-gray-700">{{ $reply->content }}</p>
                                                    
                                                    @auth
                                                        @can('delete', $reply)
                                                            <form method="POST" action="{{ route('comments.destroy', $reply) }}" class="inline mt-1" onsubmit="return confirm('Are you sure?')">
                                                                @csrf
                                                                @method('DELETE')
                                                                <button type="submit" class="text-xs text-red-600 hover:text-red-800">
                                                                    <i class="fas fa-trash mr-1"></i>Delete
                                                                </button>
                                                            </form>
                                                        @endcan
                                                    @endauth
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @else
            <div class="text-center py-8">
                <i class="fas fa-comments text-4xl text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No comments yet</h3>
                <p class="text-gray-600">Be the first to share your thoughts about this resource.</p>
            </div>
        @endif
    </div>
</div>

<script>
    function ratingForm(initialRating = 0) {
        return {
            rating: initialRating,
            
            setRating(value) {
                this.rating = value;
            }
        }
    }
</script>
