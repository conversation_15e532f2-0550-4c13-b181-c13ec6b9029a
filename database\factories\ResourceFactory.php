<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Resource>
 */
class ResourceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => \App\Models\User::factory(),
            'type' => $this->faker->randomElement(['syllabus', 'book', 'slide', 'note', 'lab_work']),
            'title' => $this->faker->sentence(4),
            'description' => $this->faker->paragraph(),
            'visibility' => $this->faker->randomElement(['public', 'private']),
            'share_token' => \Illuminate\Support\Str::random(32),
            'is_active' => true,
            'download_count' => $this->faker->numberBetween(0, 100),
            'average_rating' => $this->faker->optional(0.7)->randomFloat(2, 1, 5),
            'rating_count' => $this->faker->numberBetween(0, 20),
        ];
    }
}
