<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['name', 'value' => '', 'placeholder' => 'Enter content here...', 'required' => false, 'height' => '300']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['name', 'value' => '', 'placeholder' => 'Enter content here...', 'required' => false, 'height' => '300']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div class="wysiwyg-editor" x-data="{ editorId: '<?php echo e($name); ?>_' + Math.random().toString(36).substr(2, 9) }">
    <textarea :id="editorId" name="<?php echo e($name); ?>" class="tinymce-editor" <?php echo e($required ? 'required' : ''); ?>><?php echo e($value); ?></textarea>
</div>

<?php $__env->startPush('scripts'); ?>
<script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize TinyMCE for all editors on the page
    tinymce.init({
        selector: '.tinymce-editor',
        height: <?php echo e($height); ?>,
        menubar: false,
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'media', 'table', 'help', 'wordcount'
        ],
        toolbar: 'undo redo | blocks | ' +
            'bold italic underline strikethrough | alignleft aligncenter ' +
            'alignright alignjustify | bullist numlist outdent indent | ' +
            'removeformat | link | help',
        content_style: `
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                font-size: 14px;
                line-height: 1.6;
            }
            h1 { font-size: 2em; font-weight: bold; margin-bottom: 0.5em; }
            h2 { font-size: 1.5em; font-weight: bold; margin-bottom: 0.5em; }
            h3 { font-size: 1.25em; font-weight: bold; margin-bottom: 0.5em; }
            h4 { font-size: 1.1em; font-weight: bold; margin-bottom: 0.5em; }
            p { margin-bottom: 1em; }
            ul, ol { margin-bottom: 1em; padding-left: 2em; }
            li { margin-bottom: 0.25em; }
        `,
        placeholder: '<?php echo e($placeholder); ?>',
        branding: false,
        promotion: false,
        setup: function (editor) {
            editor.on('change', function () {
                editor.save();
            });
        }
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH D:\web\SaaS Course\CourseLaravel\resources\views/components/wysiwyg-editor.blade.php ENDPATH**/ ?>